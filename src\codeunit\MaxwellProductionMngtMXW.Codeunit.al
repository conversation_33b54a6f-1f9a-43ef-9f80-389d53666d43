
codeunit 60002 "Maxwell Production Mngt.MXW"
{
    SingleInstance = true;


    procedure SetDefaultOutputLocationOnProductionOrder(var Rec: Record "Production Order"; RunTrigger: Boolean)
    var
        MaxwellSetup: Record "Maxwell Setup MXW";
    begin
        if not RunTrigger then
            exit;

        if MaxwellSetup.Get() then
            if MaxwellSetup."Default Output Location Code" <> '' then
                if Rec."Location Code" = '' then begin
                    Rec.Validate("Location Code", MaxwellSetup."Default Output Location Code");
                    Rec.Modify(true);
                end;
    end;

    procedure PreparePackageCreationFromProdOrderLine(ProdOrderLine: Record "Prod. Order Line")
    var
        TempPackageCreation: Record "Package Creation MXW" temporary;
        Item: Record Item;
        NoSeries: Codeunit "No. Series";
        ItemNotFoundErr: Label 'Item %1 not found.', Comment = '%1="ProdOrderLine.Item No."';
    begin
        if not Item.Get(ProdOrderLine."Item No.") then
            Error(ItemNotFoundErr, ProdOrderLine."Item No.");

        // Assign lot number to Production Order Line if not already assigned
        if ProdOrderLine."Lot No. MXW" = '' then begin
            Item.TestField("Lot Nos.");
            ProdOrderLine."Lot No. MXW" := NoSeries.GetNextNo(Item."Lot Nos.");
            ProdOrderLine.Modify(true);
        end;

        // Validate that expiration date is set
        ProdOrderLine.TestField("Expiration Date MXW");

        TempPackageCreation.Init();
        TempPackageCreation."Source Type" := TempPackageCreation."Source Type"::Production;
        TempPackageCreation."Document No." := ProdOrderLine."Prod. Order No.";
        TempPackageCreation."Document Line No." := ProdOrderLine."Line No.";
        TempPackageCreation.Validate("Item No.", ProdOrderLine."Item No.");
        TempPackageCreation.Validate("Variant Code", ProdOrderLine."Variant Code");
        TempPackageCreation.Validate("Lot No.", ProdOrderLine."Lot No. MXW");
        TempPackageCreation.Validate("Expiration Date", ProdOrderLine."Expiration Date MXW");
        TempPackageCreation.Validate("Item Pieces Of Pallet", Item."Packages Per Pallet MXW");
        TempPackageCreation.Validate("Order Quantity", ProdOrderLine.Quantity);
        TempPackageCreation.Validate("Max Available Quantity", ProdOrderLine.Quantity);
        TempPackageCreation.Validate("Creation Method", TempPackageCreation."Creation Method"::Single);
        TempPackageCreation.Validate("Package Count", 1);
        TempPackageCreation.Validate("Package Quantity", Item."Packages Per Pallet MXW");
        TempPackageCreation.Insert(false);

        Page.Run(Page::"Package Creation MXW", TempPackageCreation);
    end;

    // Placeholder for future logic
    procedure CreateProductionOrderLineDetailsFromPackageCreation(var TempPackageCreation: Record "Package Creation MXW" temporary): Code[50]
    var
        ProdOrderLine: Record "Prod. Order Line";
        ProdOrderLineDetail: Record "Prod. Order Line Detail MXW";
        // PackageNoInformation: Record "Package No. Information"; // Unused variable removed
        InventorySetup: Record "Inventory Setup";
        NoSeries: Codeunit "No. Series";
        PackageNo: Code[50];
        // TODO: Add ProducedBy, Machine/Line No., and other fields if needed
        ProdOrderExeededErr: Label 'You exceeded the production order Max Available Quantity.';
    begin
        // Validate available quantity
        if TempPackageCreation."Max Available Quantity" < (TempPackageCreation."Package Count" * TempPackageCreation."Package Quantity") then
            Error(ProdOrderExeededErr);

        // Get production order line
        ProdOrderLine.Get(ProdOrderLine.Status::Released, TempPackageCreation."Document No.", TempPackageCreation."Document Line No.");
        ProdOrderLine.TestField("Location Code");

        // Get package number from No. Series
        InventorySetup.Get();
        InventorySetup.TestField("Package Nos.");
        PackageNo := NoSeries.GetNextNo(InventorySetup."Package Nos.");

        // Create Prod. Order Line Detail
        ProdOrderLineDetail.Init();
        ProdOrderLineDetail.Status := ProdOrderLineDetail.Status::Released;
        ProdOrderLineDetail."Prod. Order No." := TempPackageCreation."Document No.";
        ProdOrderLineDetail."Prod. Order Line No." := TempPackageCreation."Document Line No.";
        ProdOrderLineDetail."Package No." := PackageNo;
        ProdOrderLineDetail.Validate("Item No.", TempPackageCreation."Item No.");
        ProdOrderLineDetail.Validate("Variant Code", TempPackageCreation."Variant Code");
        ProdOrderLineDetail."Lot No." := TempPackageCreation."Lot No.";
        ProdOrderLineDetail."Expiration Date" := TempPackageCreation."Expiration Date";
        ProdOrderLineDetail.Quantity := TempPackageCreation."Package Quantity";
        ProdOrderLineDetail.Validate("Location Code", ProdOrderLine."Location Code");
        // TODO: Add ProducedBy, Machine/Line No., etc. if required by business logic
        ProdOrderLineDetail.Insert(true);


        // Create Package No. Information (extended)
        CreateLotNoInformationFromProdOrderLineDetail(ProdOrderLineDetail);
        CreatePackageNoInformationFromProdOrderLineDetail(ProdOrderLineDetail);
        // TODO: Implement posting of consumption/output journals and quality control if required

        //Commit();
        exit(PackageNo);
    end;

    procedure CreatePackageNoInformationFromProdOrderLineDetail(ProdOrderLineDetail: Record "Prod. Order Line Detail MXW")
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        PackageNoInformation.Init();
        PackageNoInformation."Package No." := ProdOrderLineDetail."Package No.";
        PackageNoInformation."Item No." := ProdOrderLineDetail."Item No.";
        PackageNoInformation."Variant Code" := ProdOrderLineDetail."Variant Code";
        PackageNoInformation.Insert(true);

        PackageNoInformation.Validate("Lot No. MXW", ProdOrderLineDetail."Lot No.");
        PackageNoInformation.Validate("Document No. MXW", ProdOrderLineDetail."Prod. Order No.");
        PackageNoInformation.Validate("Description", ProdOrderLineDetail.Description);
        PackageNoInformation.Validate("Expiration Date MXW", ProdOrderLineDetail."Expiration Date");
        PackageNoInformation.Validate("Label Quantity MXW", ProdOrderLineDetail.Quantity);
        // TODO: Add ProducedBy, Machine/Line No., Created At, etc. if required and available in Maxwell extension
        PackageNoInformation.Modify(true);
        // Create Lot No. Information for production
        //CreateLotNoInformationFromProdOrderLineDetail(ProdOrderLineDetail);
    end;

    // New procedure to create Lot No. Information from production order line detail
    local procedure CreateLotNoInformationFromProdOrderLineDetail(ProdOrderLineDetail: Record "Prod. Order Line Detail MXW")
    var
        LotNoInformation: Record "Lot No. Information";
        Item: Record Item;
        BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
    begin
        if not LotNoInformation.Get(ProdOrderLineDetail."Item No.", ProdOrderLineDetail."Variant Code", ProdOrderLineDetail."Lot No.") then begin
            LotNoInformation.Init();
            LotNoInformation."Item No." := ProdOrderLineDetail."Item No.";
            LotNoInformation."Variant Code" := ProdOrderLineDetail."Variant Code";
            LotNoInformation."Lot No." := ProdOrderLineDetail."Lot No.";

            // Populate description based on item/variant using helper
            if Item.Get(ProdOrderLineDetail."Item No.") then
                LotNoInformation.Description := BasicFuncs.GetItemDescription(ProdOrderLineDetail."Item No.", ProdOrderLineDetail."Variant Code");

            // Set production order reference
            LotNoInformation."Production Order No. MXW" := ProdOrderLineDetail."Prod. Order No.";
            LotNoInformation."Expiration Date MXW" := ProdOrderLineDetail."Expiration Date";

            LotNoInformation.Insert(true);
        end;
    end;

    procedure CreateOutputJournalsFromProdOrderLineDetail(ProdOrderLineDetail: Record "Prod. Order Line Detail MXW")
    var
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        MaxwellSetup: Record "Maxwell Setup MXW";
        OutputJnlExplRoute: Codeunit "Output Jnl.-Expl. Route";
        ItemJournalLineNo: Integer;
    begin
        ProdOrderLineDetail.TestField(Posted, false);

        MaxwellSetup.GetRecordOnce();
        MaxwellSetup.TestField("Output Journal Template MXW");
        MaxwellSetup.TestField("Output Journal Batch MXW");

        LastItemJournalLine.SetRange("Journal Template Name", MaxwellSetup."Output Journal Template MXW");
        LastItemJournalLine.SetRange("Journal Batch Name", MaxwellSetup."Output Journal Batch MXW");
        if LastItemJournalLine.FindLast() then
            ItemJournalLineNo := LastItemJournalLine."Line No." + 10000
        else
            ItemJournalLineNo := 10000;

        ItemJournalLine.Init();
        ItemJournalLine.Validate("Journal Template Name", MaxwellSetup."Output Journal Template MXW");
        ItemJournalLine.Validate("Journal Batch Name", MaxwellSetup."Output Journal Batch MXW");
        ItemJournalLine.Validate("Line No.", ItemJournalLineNo);
        ItemJournalLine.SetUpNewLine(LastItemJournalLine);
        ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::Output);
        ItemJournalLine.Insert(true);

        ItemJournalLine.Validate("Order No.", ProdOrderLineDetail."Prod. Order No.");
        ItemJournalLine.Validate("Item No.", ProdOrderLineDetail."Item No.");
        ItemJournalLine.Validate("Variant Code", ProdOrderLineDetail."Variant Code");
        ItemJournalLine.Modify(true);

        // Set global variables for event subscriber
        SetGlobalOutputValues(ProdOrderLineDetail."Lot No.", ProdOrderLineDetail."Package No.", ProdOrderLineDetail.Quantity, ProdOrderLineDetail."Expiration Date");
        OutputJnlExplRoute.Run(ItemJournalLine);
        ClearGlobalOutputValues();

        // Open the Output Journal page for testing
        ItemJournalLine.Reset();
        ItemJournalLine.SetRange("Journal Template Name", MaxwellSetup."Output Journal Template MXW");
        ItemJournalLine.SetRange("Journal Batch Name", MaxwellSetup."Output Journal Batch MXW");
        Page.Run(Page::"Output Journal", ItemJournalLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnBeforeOutputItemJnlLineInsert, '', false, false)]
    local procedure OnBeforeOutputItemJnlLineInsert(var ItemJournalLine: Record "Item Journal Line"; LastOperation: Boolean)
    begin
        ItemJournalLine.Validate("Output Quantity", GlobalOutputQty);

        // if not ItemJournalLine.LastOutputOperation(ItemJournalLine) then begin
        //     ItemJournalLine.Validate("Lot No.", '');
        //     ItemJournalLine.Validate("Package No.", '');
        // end;
    end;

    // procedure CreateConsumptionJournalsFromProdOrderLineDetail(ProdOrderLineDetail: Record "Prod. Order Line Detail MXW")
    // var
    //     ProdOrderComponent: Record "Prod. Order Component";
    //     ItemJournalLine: Record "Item Journal Line";
    //     MaxwellSetup: Record "Maxwell Setup MXW";
    //     CalcConsumption: Report "Calc. Consumption";
    //     CalcBasedOn: Option "Actual Output","Expected Output";
    // begin
    //     ProdOrderLineDetail.TestField(Posted, false);

    //     MaxwellSetup.GetRecordOnce();
    //     MaxwellSetup.TestField("Consumption Jnl. Template MXW");
    //     MaxwellSetup.TestField("Consumption Jnl. Batch MXW");

    //     ProdOrderComponent.SetRange("Prod. Order No.", ProdOrderLineDetail."Prod. Order No.");
    //     ProdOrderComponent.SetRange("Prod. Order Line No.", ProdOrderLineDetail."Prod. Order Line No.");

    //     CalcConsumption.UseRequestPage(false);
    //     CalcConsumption.SetTableView(ProdOrderComponent);
    //     CalcConsumption.InitializeRequest(WorkDate(), CalcBasedOn::"Expected Output");
    //     CalcConsumption.SetTemplateAndBatchName(MaxwellSetup."Consumption Jnl. Template MXW", MaxwellSetup."Consumption Jnl. Batch MXW");
    //     GlobalOutputQty := ProdOrderLineDetail.Quantity;
    //     CalcConsumption.RunModal();
    //     GlobalOutputQty := 0;
    //     // Automatically open the Consumption Journal page filtered to the correct template and batch
    //     ItemJournalLine.SetRange("Journal Template Name", MaxwellSetup."Consumption Jnl. Template MXW");
    //     ItemJournalLine.SetRange("Journal Batch Name", MaxwellSetup."Consumption Jnl. Batch MXW");
    //     Page.Run(Page::"Consumption Journal", ItemJournalLine);
    // end;

    // procedure PostConsumptionAndOutputJournals(var ProdOrderLineDetail: Record "Prod. Order Line Detail MXW")
    // var
    //     ItemJournalLine: Record "Item Journal Line";
    //     MaxwellSetup: Record "Maxwell Setup MXW";
    //     // ConsumptionErr: Label 'Something wrong with journal entries, please check Consumption Lines.';
    //     ProductionErr: Label 'Something wrong with journal entries, please check Production Lines.';
    // begin
    //     MaxwellSetup.GetRecordOnce();

    //     // Post consumption journals - COMMENTED OUT
    //     // ItemJournalLine.SetRange("Journal Template Name", MaxwellSetup."Consumption Jnl. Template MXW");
    //     // ItemJournalLine.SetRange("Journal Batch Name", MaxwellSetup."Consumption Jnl. Batch MXW");
    //     // if not ItemJournalLine.FindFirst() then
    //     //     Error(ConsumptionErr);
    //     // Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);

    //     // Post output journals
    //     ItemJournalLine.Reset();
    //     ItemJournalLine.SetRange("Journal Template Name", MaxwellSetup."Output Journal Template MXW");
    //     ItemJournalLine.SetRange("Journal Batch Name", MaxwellSetup."Output Journal Batch MXW");
    //     if not ItemJournalLine.FindFirst() then
    //         Error(ProductionErr);

    //     Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);

    //     ProdOrderLineDetail.Validate(Posted, true);
    //     ProdOrderLineDetail.Modify(true);
    // end;

    // Global variables for output journal processing
    var
        GlobalExprationDate: Date;
        GlobalLotNo: Code[50];
        GlobalPackageNo: Code[50];
        GlobalOutputQty: Decimal;


    // procedure HandleCalcConsumptionOnBeforeGetNeededQty(var NeededQty: Decimal; CalcBasedOn: Option; ProdOrderComponent: Record "Prod. Order Component"; ProductionOrder: Record "Production Order"; PostingDate: Date; var IsHandled: Boolean)
    // begin
    //     if GlobalOutputQty > 0 then begin
    //         IsHandled := true;
    //         NeededQty := ProdOrderComponent."Quantity per" * GlobalOutputQty;
    //     end;
    // end;


    procedure HandleOutputJnlExplRouteOnBeforeOutputItemJnlLineInsert(var ItemJournalLine: Record "Item Journal Line"; LastOperation: Boolean)
    begin
        // if GlobalOutputQty > 0 then
        //     ItemJournalLine.Validate("Output Quantity", GlobalOutputQty);
    end;

    local procedure SetGlobalOutputValues(LotNo: Code[50]; PackageNo: Code[50]; OutputQty: Decimal; ExprationDate: Date)
    begin
        GlobalOutputQty := OutputQty;
        GlobalLotNo := LotNo;
        GlobalPackageNo := PackageNo;
        GlobalExprationDate := ExprationDate;
    end;

    local procedure ClearGlobalOutputValues()
    begin
        GlobalOutputQty := 0;
        GlobalExprationDate := 0D;
        GlobalLotNo := '';
        GlobalPackageNo := '';
    end;


    procedure HandleOnAfterInsertItemJnlLine(var ItemJournalLine: Record "Item Journal Line")
    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
    begin
        if ItemJournalLine.LastOutputOperation(ItemJournalLine) then
            MaxwellPurchaseManagement.AssignLotNoToItemJournalLine(ItemJournalLine, GlobalLotNo, ItemJournalLine."Output Quantity (Base)", ItemJournalLine."Output Quantity", GlobalPackageNo, GlobalExprationDate);
    end;

    procedure HandleOnAfterInsertItemLedgerEntry(var ItemLedgerEntry: Record "Item Ledger Entry")
    var
        ProdOrderLineDetail: Record "Prod. Order Line Detail MXW";
    begin
        if (ItemLedgerEntry."Entry Type" = ItemLedgerEntry."Entry Type"::Output) and (ItemLedgerEntry."Package No." <> '') then begin
            ProdOrderLineDetail.SetRange("Package No.", ItemLedgerEntry."Package No.");
            if ProdOrderLineDetail.FindFirst() then begin
                ProdOrderLineDetail.Posted := true;
                ProdOrderLineDetail.Modify(true);
            end;
        end;
    end;


    // procedure HandleCalcConsumptionOnAfterCreateConsumpJnlLine(LocationCode: Code[10]; BinCode: Code[20]; QtyToPost: Decimal; var ItemJournalLine: Record "Item Journal Line")
    // begin
    //     AssignItemTrackingInformationFromConsumptionLine(ItemJournalLine);
    // end;

    // local procedure AssignItemTrackingInformationFromConsumptionLine(var ItemJnlLine: Record "Item Journal Line")
    // var
    //     ItemLedgerEntry: Record "Item Ledger Entry";
    //     MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
    //     DynamicQty: Decimal;
    //     TempGlobalQty: Decimal;
    //     QtyIsNotEnoughErr: Label 'Item No.: %1, Variant Code: %2 - %4 is not enough in %3', Comment = 'Item No.: %1, Variant Code: %2 is not enough in %3, %4 - Description';
    // begin
    //     ItemLedgerEntry.SetRange("Item No.", ItemJnlLine."Item No.");
    //     ItemLedgerEntry.SetRange("Variant Code", ItemJnlLine."Variant Code");
    //     ItemLedgerEntry.SetRange("Location Code", ItemJnlLine."Location Code");
    //     ItemLedgerEntry.SetRange(Open, true);
    //     ItemLedgerEntry.SetCurrentKey("Lot No.");
    //     ItemLedgerEntry.SetAscending("Lot No.", true);

    //     DynamicQty := ItemJnlLine."Quantity (Base)";
    //     TempGlobalQty := 0;
    //     if ItemLedgerEntry.FindSet() then
    //         repeat
    //             if HandleTempGlobalQty(TempGlobalQty, ItemLedgerEntry) then
    //                 continue;

    //             if HandleDynamicQty(DynamicQty, TempGlobalQty, ItemLedgerEntry, ItemJnlLine, MaxwellPurchaseManagement) then
    //                 TempGlobalQty := 0
    //             else
    //                 break;
    //         until (ItemLedgerEntry.Next() = 0);

    //     if DynamicQty <> 0 then
    //         Error(QtyIsNotEnoughErr, ItemJnlLine."Item No.", ItemJnlLine."Variant Code", ItemJnlLine."Location Code", ItemJnlLine.Description);
    // end;

    // local procedure HandleTempGlobalQty(var TempGlobalQty: Decimal; ItemLedgerEntry: Record "Item Ledger Entry"): Boolean
    // begin
    //     if TempGlobalQty > 0 then
    //         if TempGlobalQty > ItemLedgerEntry."Remaining Quantity" then begin
    //             TempGlobalQty -= ItemLedgerEntry."Remaining Quantity";
    //             exit(true);
    //         end;
    //     exit(false);
    // end;

    // local procedure HandleDynamicQty(var DynamicQty: Decimal; TempGlobalQty: Decimal; ItemLedgerEntry: Record "Item Ledger Entry"; var ItemJnlLine: Record "Item Journal Line"; MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW"): Boolean
    // var
    //     RemainingQty: Decimal;
    // begin
    //     RemainingQty := ItemLedgerEntry."Remaining Quantity" - TempGlobalQty;
    //     if (DynamicQty > RemainingQty) then begin
    //         MaxwellPurchaseManagement.AssignLotNoToItemJournalLine(ItemJnlLine, ItemLedgerEntry."Lot No.", RemainingQty, RemainingQty, ItemLedgerEntry."Package No.", 0D);
    //         DynamicQty -= RemainingQty;
    //         exit(true);
    //     end else begin
    //         MaxwellPurchaseManagement.AssignLotNoToItemJournalLine(ItemJnlLine, ItemLedgerEntry."Lot No.", DynamicQty, DynamicQty, ItemLedgerEntry."Package No.", 0D);
    //         DynamicQty := 0;
    //         exit(false);
    //     end;
    // end;
}
