
table 60011 "Prod. Order Line Detail MXW"
{
    Caption = 'Prod. Order Line Detail';
    DataClassification = CustomerContent;
    LookupPageId = "Prod. Order Line Details MXW";
    DrillDownPageId = "Prod. Order Line Details MXW";

    fields
    {
        field(1; Status; Enum "Production Order Status")
        {
            Caption = 'Status';
            ToolTip = 'Specifies the status of the production order.';
            AllowInCustomizations = Always;
        }
        field(2; "Prod. Order No."; Code[20])
        {
            Caption = 'Prod. Order No.';
            TableRelation = "Production Order"."No." where(Status = field(Status));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the production order number.';
        }
        field(3; "Prod. Order Line No."; Integer)
        {
            Caption = 'Prod. Order Line No.';
            ToolTip = 'Specifies the production order line number.';
            AllowInCustomizations = Always;
        }
        field(4; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the line number.';
            AllowInCustomizations = Always;
        }
        field(5; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the package number.';
            TableRelation = "Package No. Information"."Package No.";
        }
        field(6; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the item number.';
            TableRelation = "Item"."No.";
            trigger OnValidate()
            var
                Item: Record Item;
                BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
            begin
                if Item.Get(Rec."Item No.") then
                    Rec.Description := BasicFuncs.GetItemDescription(Rec."Item No.", '');
            end;
        }
        field(7; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the variant code.';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
        }
        field(8; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the description.';
        }
        field(9; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the lot number.';
        }
        field(10; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the quantity.';
        }
        field(11; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            ToolTip = 'Specifies the location code.';
            TableRelation = Location.Code;
        }
        field(12; Posted; Boolean)
        {
            Caption = 'Posted';
            ToolTip = 'Specifies if the line is posted.';
        }
        field(13; "Expiration Date"; Date)
        {
            Caption = 'Expiration Date';
            ToolTip = 'Specifies the expiration date.';
        }
    }
    keys
    {
        key(PK; Status, "Prod. Order No.", "Prod. Order Line No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        ProdOrderLineDetail: Record "Prod. Order Line Detail MXW";
    begin
        ProdOrderLineDetail.SetRange(Status, Rec.Status);
        ProdOrderLineDetail.SetRange("Prod. Order No.", Rec."Prod. Order No.");
        ProdOrderLineDetail.SetRange("Prod. Order Line No.", Rec."Prod. Order Line No.");
        if ProdOrderLineDetail.FindLast() then
            Rec."Line No." := ProdOrderLineDetail."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    trigger OnDelete()
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        Rec.TestField(Posted, false);
        if PackageNoInformation.Get(Rec."Item No.", Rec."Variant Code", Rec."Package No.") then
            PackageNoInformation.Delete(true);
    end;
}
